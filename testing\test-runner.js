/**
 * ATMA Backend Test Runner
 * 
 * Unified test runner for E2E and Load tests with comprehensive reporting
 * and configuration management.
 */

const { E2ETestSuite } = require('./e2e-tests');
const { LoadTestRunner } = require('./load-tests');
const fs = require('fs');
const path = require('path');

class TestRunner {
    constructor() {
        this.results = {
            e2e: null,
            load: null,
            summary: null
        };
        this.startTime = null;
        this.endTime = null;
    }

    async runAllTests(options = {}) {
        const {
            runE2E = true,
            runLoad = true,
            generateReport = true,
            outputDir = './test-results'
        } = options;

        console.log('🚀 ATMA Backend Test Suite Starting...\n');
        this.startTime = Date.now();

        try {
            // Ensure output directory exists
            if (generateReport && !fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            // Run E2E tests
            if (runE2E) {
                console.log('=' .repeat(60));
                console.log('🔍 RUNNING END-TO-END TESTS');
                console.log('=' .repeat(60));
                
                const e2eTestSuite = new E2ETestSuite();
                await e2eTestSuite.runAllTests();
                this.results.e2e = { status: 'passed', timestamp: new Date().toISOString() };
                
                console.log('\n✅ E2E Tests completed successfully!\n');
            }

            // Run Load tests
            if (runLoad) {
                console.log('=' .repeat(60));
                console.log('⚡ RUNNING LOAD TESTS');
                console.log('=' .repeat(60));
                
                const loadTestRunner = new LoadTestRunner();
                this.results.load = await loadTestRunner.runAllLoadTests();
                
                console.log('\n✅ Load Tests completed successfully!\n');
            }

            this.endTime = Date.now();
            this.generateSummary();

            // Generate reports
            if (generateReport) {
                await this.generateReports(outputDir);
            }

            this.printFinalSummary();
            return this.results;

        } catch (error) {
            this.endTime = Date.now();
            console.error('\n❌ Test suite failed:', error.message);
            
            if (generateReport) {
                await this.generateErrorReport(outputDir, error);
            }
            
            throw error;
        }
    }

    generateSummary() {
        const totalDuration = this.endTime - this.startTime;
        
        this.results.summary = {
            totalDuration,
            startTime: new Date(this.startTime).toISOString(),
            endTime: new Date(this.endTime).toISOString(),
            e2eStatus: this.results.e2e ? 'passed' : 'skipped',
            loadTestStatus: this.results.load ? 'passed' : 'skipped',
            overallStatus: 'passed'
        };

        // Add load test summary if available
        if (this.results.load) {
            const loadSummary = {
                totalTests: Object.keys(this.results.load).length,
                totalRequests: 0,
                totalSuccessfulRequests: 0,
                averageResponseTime: 0,
                averageRequestsPerSecond: 0
            };

            Object.values(this.results.load).forEach(testResult => {
                if (testResult.totalRequests) {
                    loadSummary.totalRequests += testResult.totalRequests;
                    loadSummary.totalSuccessfulRequests += testResult.successfulRequests;
                    loadSummary.averageResponseTime += testResult.avgResponseTime;
                    loadSummary.averageRequestsPerSecond += testResult.requestsPerSecond;
                }
            });

            loadSummary.averageResponseTime = Math.round(loadSummary.averageResponseTime / loadSummary.totalTests);
            loadSummary.averageRequestsPerSecond = Math.round((loadSummary.averageRequestsPerSecond / loadSummary.totalTests) * 100) / 100;
            loadSummary.overallSuccessRate = Math.round((loadSummary.totalSuccessfulRequests / loadSummary.totalRequests) * 100 * 100) / 100;

            this.results.summary.loadTestSummary = loadSummary;
        }
    }

    async generateReports(outputDir) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        // Generate JSON report
        const jsonReport = {
            metadata: {
                testSuite: 'ATMA Backend Tests',
                version: '1.0.0',
                timestamp: new Date().toISOString(),
                duration: this.endTime - this.startTime
            },
            results: this.results
        };

        const jsonPath = path.join(outputDir, `test-results-${timestamp}.json`);
        fs.writeFileSync(jsonPath, JSON.stringify(jsonReport, null, 2));
        console.log(`📄 JSON report saved: ${jsonPath}`);

        // Generate HTML report
        const htmlReport = this.generateHTMLReport(jsonReport);
        const htmlPath = path.join(outputDir, `test-results-${timestamp}.html`);
        fs.writeFileSync(htmlPath, htmlReport);
        console.log(`📄 HTML report saved: ${htmlPath}`);

        // Generate CSV report for load test results
        if (this.results.load) {
            const csvReport = this.generateCSVReport();
            const csvPath = path.join(outputDir, `load-test-results-${timestamp}.csv`);
            fs.writeFileSync(csvPath, csvReport);
            console.log(`📄 CSV report saved: ${csvPath}`);
        }
    }

    generateHTMLReport(jsonReport) {
        const { metadata, results } = jsonReport;
        
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATMA Backend Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-skipped { color: #6c757d; }
        .metric-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { color: #6c757d; font-size: 14px; }
        .test-section { margin: 20px 0; }
        .test-section h3 { border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .json-data { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        pre { margin: 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ATMA Backend Test Results</h1>
            <p><strong>Generated:</strong> ${metadata.timestamp}</p>
            <p><strong>Duration:</strong> ${Math.round(metadata.duration / 1000)}s</p>
        </div>

        <div class="test-section">
            <h3>📊 Test Summary</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div class="metric-card">
                    <div class="metric-value status-${results.summary.e2eStatus}">${results.summary.e2eStatus.toUpperCase()}</div>
                    <div class="metric-label">E2E Tests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value status-${results.summary.loadTestStatus}">${results.summary.loadTestStatus.toUpperCase()}</div>
                    <div class="metric-label">Load Tests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Math.round(metadata.duration / 1000)}s</div>
                    <div class="metric-label">Total Duration</div>
                </div>
            </div>
        </div>

        ${results.summary.loadTestSummary ? `
        <div class="test-section">
            <h3>⚡ Load Test Summary</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div class="metric-card">
                    <div class="metric-value">${results.summary.loadTestSummary.totalRequests}</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${results.summary.loadTestSummary.overallSuccessRate}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${results.summary.loadTestSummary.averageResponseTime}ms</div>
                    <div class="metric-label">Avg Response Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${results.summary.loadTestSummary.averageRequestsPerSecond}</div>
                    <div class="metric-label">Requests/Second</div>
                </div>
            </div>
        </div>
        ` : ''}

        ${results.load ? `
        <div class="test-section">
            <h3>📈 Detailed Load Test Results</h3>
            <table>
                <thead>
                    <tr>
                        <th>Test Name</th>
                        <th>Total Requests</th>
                        <th>Success Rate</th>
                        <th>Avg Response Time</th>
                        <th>Requests/Second</th>
                        <th>P95 Response Time</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.entries(results.load).map(([testName, testResult]) => `
                    <tr>
                        <td>${testName}</td>
                        <td>${testResult.totalRequests || 'N/A'}</td>
                        <td>${testResult.successRate ? testResult.successRate.toFixed(2) + '%' : 'N/A'}</td>
                        <td>${testResult.avgResponseTime || 'N/A'}ms</td>
                        <td>${testResult.requestsPerSecond || 'N/A'}</td>
                        <td>${testResult.p95ResponseTime || 'N/A'}ms</td>
                    </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ` : ''}

        <div class="test-section">
            <h3>📋 Raw Test Data</h3>
            <div class="json-data">
                <pre>${JSON.stringify(results, null, 2)}</pre>
            </div>
        </div>
    </div>
</body>
</html>`;
    }

    generateCSVReport() {
        if (!this.results.load) return '';

        const headers = ['Test Name', 'Total Requests', 'Successful Requests', 'Failed Requests', 'Success Rate (%)', 'Avg Response Time (ms)', 'Min Response Time (ms)', 'Max Response Time (ms)', 'P50 (ms)', 'P95 (ms)', 'P99 (ms)', 'Requests/Second', 'Duration (ms)'];
        
        const rows = Object.entries(this.results.load).map(([testName, testResult]) => [
            testName,
            testResult.totalRequests || 0,
            testResult.successfulRequests || 0,
            testResult.failedRequests || 0,
            testResult.successRate ? testResult.successRate.toFixed(2) : 0,
            testResult.avgResponseTime || 0,
            testResult.minResponseTime || 0,
            testResult.maxResponseTime || 0,
            testResult.p50ResponseTime || 0,
            testResult.p95ResponseTime || 0,
            testResult.p99ResponseTime || 0,
            testResult.requestsPerSecond || 0,
            testResult.duration || 0
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    async generateErrorReport(outputDir, error) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const errorReport = {
            timestamp: new Date().toISOString(),
            error: {
                message: error.message,
                stack: error.stack
            },
            partialResults: this.results
        };

        const errorPath = path.join(outputDir, `error-report-${timestamp}.json`);
        fs.writeFileSync(errorPath, JSON.stringify(errorReport, null, 2));
        console.log(`📄 Error report saved: ${errorPath}`);
    }

    printFinalSummary() {
        console.log('\n' + '=' .repeat(60));
        console.log('🎉 ATMA BACKEND TEST SUITE COMPLETED');
        console.log('=' .repeat(60));
        
        console.log(`⏱️  Total Duration: ${Math.round((this.endTime - this.startTime) / 1000)}s`);
        console.log(`🔍 E2E Tests: ${this.results.summary.e2eStatus.toUpperCase()}`);
        console.log(`⚡ Load Tests: ${this.results.summary.loadTestStatus.toUpperCase()}`);
        
        if (this.results.summary.loadTestSummary) {
            const summary = this.results.summary.loadTestSummary;
            console.log(`📊 Total Requests: ${summary.totalRequests}`);
            console.log(`✅ Success Rate: ${summary.overallSuccessRate}%`);
            console.log(`⚡ Avg Response Time: ${summary.averageResponseTime}ms`);
            console.log(`🚀 Avg Requests/Second: ${summary.averageRequestsPerSecond}`);
        }
        
        console.log('\n🎯 All tests completed successfully!');
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);
    const options = {
        runE2E: !args.includes('--no-e2e'),
        runLoad: !args.includes('--no-load'),
        generateReport: !args.includes('--no-report'),
        outputDir: args.includes('--output') ? args[args.indexOf('--output') + 1] : './test-results'
    };

    const runner = new TestRunner();
    
    try {
        await runner.runAllTests(options);
        process.exit(0);
    } catch (error) {
        console.error('Test suite failed:', error.message);
        process.exit(1);
    }
}

// Export for use in other files
module.exports = {
    TestRunner
};

// Run if this file is executed directly
if (require.main === module) {
    main();
}
