/**
 * ATMA Backend E2E Testing Suite
 * 
 * This file contains comprehensive end-to-end tests for the ATMA API Gateway
 * and all connected services based on API_external.md documentation.
 * 
 * Test Coverage:
 * - Authentication & User Management
 * - Admin Management
 * - Assessment Service
 * - Archive Service
 * - Health & Monitoring
 * - Error Handling
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
    BASE_URL: 'http://localhost:3000',
    TIMEOUT: 30000,
    TEST_USER: {
        email: `test-${Date.now()}@example.com`,
        password: 'testpass123'
    },
    ADMIN_USER: {
        username: 'admin',
        password: 'admin123'
    }
};

// Test state
let testState = {
    userToken: null,
    adminToken: null,
    userId: null,
    schoolId: null,
    assessmentJobId: null,
    resultId: null
};

// Utility functions
const api = axios.create({
    baseURL: CONFIG.BASE_URL,
    timeout: CONFIG.TIMEOUT,
    validateStatus: () => true // Don't throw on HTTP errors
});

const log = (message, data = null) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
    if (data) console.log(JSON.stringify(data, null, 2));
};

const assert = (condition, message) => {
    if (!condition) {
        throw new Error(`Assertion failed: ${message}`);
    }
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Test suites
class E2ETestSuite {
    async runAllTests() {
        console.log('🚀 Starting ATMA Backend E2E Tests\n');
        
        try {
            await this.testHealthChecks();
            await this.testUserAuthentication();
            await this.testAdminAuthentication();
            await this.testSchoolManagement();
            await this.testAssessmentFlow();
            await this.testArchiveService();
            await this.testErrorHandling();
            await this.testRateLimiting();
            
            console.log('\n✅ All E2E tests passed successfully!');
        } catch (error) {
            console.error('\n❌ E2E tests failed:', error.message);
            throw error;
        }
    }

    async testHealthChecks() {
        log('🔍 Testing Health Checks...');
        
        // Gateway health
        const healthChecks = [
            '/',
            '/health',
            '/health/detailed',
            '/health/ready',
            '/health/live'
        ];

        for (const endpoint of healthChecks) {
            const response = await api.get(endpoint);
            assert(response.status === 200, `Health check failed for ${endpoint}`);
            log(`✓ ${endpoint} - Status: ${response.status}`);
        }

        // Service-specific health
        const serviceHealthChecks = [
            '/api/auth/health',
            '/api/archive/health',
            '/api/assessment/health'
        ];

        for (const endpoint of serviceHealthChecks) {
            const response = await api.get(endpoint);
            assert(response.status === 200, `Service health check failed for ${endpoint}`);
            log(`✓ ${endpoint} - Status: ${response.status}`);
        }
    }

    async testUserAuthentication() {
        log('🔐 Testing User Authentication...');
        
        // Register user
        const registerResponse = await api.post('/api/auth/register', {
            email: CONFIG.TEST_USER.email,
            password: CONFIG.TEST_USER.password
        });
        
        assert(registerResponse.status === 201, 'User registration failed');
        assert(registerResponse.data.success === true, 'Registration response invalid');
        log('✓ User registration successful');

        // Login user
        const loginResponse = await api.post('/api/auth/login', {
            email: CONFIG.TEST_USER.email,
            password: CONFIG.TEST_USER.password
        });
        
        assert(loginResponse.status === 200, 'User login failed');
        assert(loginResponse.data.success === true, 'Login response invalid');
        assert(loginResponse.data.data.token, 'No token received');
        
        testState.userToken = loginResponse.data.data.token;
        testState.userId = loginResponse.data.data.user.id;
        log('✓ User login successful');

        // Get profile
        const profileResponse = await api.get('/api/auth/profile', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(profileResponse.status === 200, 'Get profile failed');
        assert(profileResponse.data.data.email === CONFIG.TEST_USER.email, 'Profile email mismatch');
        log('✓ Get user profile successful');

        // Update profile
        const updateResponse = await api.put('/api/auth/profile', {
            username: 'testuser',
            full_name: 'Test User'
        }, {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(updateResponse.status === 200, 'Profile update failed');
        log('✓ Profile update successful');

        // Get token balance
        const balanceResponse = await api.get('/api/auth/token-balance', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(balanceResponse.status === 200, 'Get token balance failed');
        assert(typeof balanceResponse.data.data.balance === 'number', 'Invalid token balance');
        log('✓ Get token balance successful');
    }

    async testAdminAuthentication() {
        log('👨‍💼 Testing Admin Authentication...');
        
        // Admin login
        const loginResponse = await api.post('/api/admin/login', {
            username: CONFIG.ADMIN_USER.username,
            password: CONFIG.ADMIN_USER.password
        });
        
        assert(loginResponse.status === 200, 'Admin login failed');
        assert(loginResponse.data.success === true, 'Admin login response invalid');
        assert(loginResponse.data.data.token, 'No admin token received');
        
        testState.adminToken = loginResponse.data.data.token;
        log('✓ Admin login successful');

        // Get admin profile
        const profileResponse = await api.get('/api/admin/profile', {
            headers: { Authorization: `Bearer ${testState.adminToken}` }
        });
        
        assert(profileResponse.status === 200, 'Get admin profile failed');
        log('✓ Get admin profile successful');
    }

    async testSchoolManagement() {
        log('🏫 Testing School Management...');
        
        // Create school
        const createResponse = await api.post('/api/auth/schools', {
            name: 'Test School E2E',
            address: 'Test Address 123',
            city: 'Jakarta',
            province: 'DKI Jakarta'
        }, {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(createResponse.status === 201, 'School creation failed');
        testState.schoolId = createResponse.data.data.id;
        log('✓ School creation successful');

        // Get schools
        const getResponse = await api.get('/api/auth/schools', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(getResponse.status === 200, 'Get schools failed');
        assert(Array.isArray(getResponse.data.data), 'Schools data is not array');
        log('✓ Get schools successful');

        // Get schools by location
        const locationResponse = await api.get('/api/auth/schools/by-location?city=Jakarta', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(locationResponse.status === 200, 'Get schools by location failed');
        log('✓ Get schools by location successful');
    }

    async testAssessmentFlow() {
        log('🎯 Testing Assessment Flow...');
        
        // Submit assessment
        const assessmentData = {
            assessmentName: 'AI-Driven Talent Mapping E2E Test',
            personalityTest: {
                responses: [1, 2, 3, 4, 5, 1, 2, 3, 4, 5],
                completedAt: new Date().toISOString()
            },
            skillsAssessment: {
                technicalSkills: { programming: 8, databases: 7 },
                softSkills: { communication: 9, leadership: 6 }
            },
            cognitiveTest: {
                logicalReasoning: { score: 85 },
                problemSolving: { score: 78 }
            }
        };

        const submitResponse = await api.post('/api/assessment/submit', assessmentData, {
            headers: {
                Authorization: `Bearer ${testState.userToken}`,
                'X-Idempotency-Key': uuidv4()
            }
        });
        
        assert(submitResponse.status === 200, 'Assessment submission failed');
        assert(submitResponse.data.data.jobId, 'No job ID received');
        
        testState.assessmentJobId = submitResponse.data.data.jobId;
        log('✓ Assessment submission successful');

        // Check assessment status
        const statusResponse = await api.get(`/api/assessment/status/${testState.assessmentJobId}`, {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(statusResponse.status === 200, 'Assessment status check failed');
        log('✓ Assessment status check successful');

        // Get queue status
        const queueResponse = await api.get('/api/assessment/queue/status', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(queueResponse.status === 200, 'Queue status check failed');
        log('✓ Queue status check successful');

        // Assessment health checks
        const healthEndpoints = [
            '/api/assessment/health',
            '/api/assessment/health/ready',
            '/api/assessment/health/live',
            '/api/assessment/health/queue'
        ];

        for (const endpoint of healthEndpoints) {
            const response = await api.get(endpoint);
            assert(response.status === 200, `Assessment health check failed for ${endpoint}`);
        }
        log('✓ Assessment health checks successful');
    }

    async testArchiveService() {
        log('📊 Testing Archive Service...');
        
        // Get user results
        const resultsResponse = await api.get('/api/archive/results?page=1&limit=10', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(resultsResponse.status === 200, 'Get results failed');
        log('✓ Get user results successful');

        // Get user jobs
        const jobsResponse = await api.get('/api/archive/jobs?page=1&limit=10', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(jobsResponse.status === 200, 'Get jobs failed');
        log('✓ Get user jobs successful');

        // Get job statistics
        const statsResponse = await api.get('/api/archive/jobs/stats', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(statsResponse.status === 200, 'Get job stats failed');
        log('✓ Get job statistics successful');

        // Get user statistics
        const userStatsResponse = await api.get('/api/archive/stats', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(userStatsResponse.status === 200, 'Get user stats failed');
        log('✓ Get user statistics successful');

        // Get user overview
        const overviewResponse = await api.get('/api/archive/stats/overview', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(overviewResponse.status === 200, 'Get overview failed');
        log('✓ Get user overview successful');

        // Unified statistics API
        const unifiedStatsResponse = await api.get('/api/archive/api/v1/stats?type=user&scope=overview&timeRange=30%20days', {
            headers: { Authorization: `Bearer ${testState.userToken}` }
        });
        
        assert(unifiedStatsResponse.status === 200, 'Unified stats failed');
        log('✓ Unified statistics API successful');
    }

    async testErrorHandling() {
        log('❌ Testing Error Handling...');
        
        // Test unauthorized access
        const unauthorizedResponse = await api.get('/api/auth/profile');
        assert(unauthorizedResponse.status === 401, 'Unauthorized access should return 401');
        log('✓ Unauthorized access handled correctly');

        // Test invalid login
        const invalidLoginResponse = await api.post('/api/auth/login', {
            email: '<EMAIL>',
            password: 'wrongpassword'
        });
        assert(invalidLoginResponse.status === 401, 'Invalid login should return 401');
        log('✓ Invalid login handled correctly');

        // Test validation error
        const validationResponse = await api.post('/api/auth/register', {
            email: 'invalid-email',
            password: '123'
        });
        assert(validationResponse.status === 400, 'Validation error should return 400');
        log('✓ Validation error handled correctly');

        // Test not found
        const notFoundResponse = await api.get('/api/nonexistent/endpoint');
        assert(notFoundResponse.status === 404, 'Not found should return 404');
        log('✓ Not found handled correctly');
    }

    async testRateLimiting() {
        log('🚦 Testing Rate Limiting...');
        
        // Note: This is a basic test - full rate limiting tests would require
        // making many requests quickly, which might be too aggressive for E2E
        const response = await api.get('/health');
        assert(response.status === 200, 'Rate limiting test failed');
        log('✓ Rate limiting test completed (basic check)');
    }
}

// Main execution
async function runE2ETests() {
    const testSuite = new E2ETestSuite();
    
    try {
        await testSuite.runAllTests();
        process.exit(0);
    } catch (error) {
        console.error('E2E Tests failed:', error);
        process.exit(1);
    }
}

// Export for use in other files
module.exports = {
    E2ETestSuite,
    CONFIG,
    api,
    log,
    assert,
    sleep
};

// Run tests if this file is executed directly
if (require.main === module) {
    runE2ETests();
}
