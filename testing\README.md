# ATMA Backend Testing Suite

Comprehensive testing suite for the ATMA (AI-Driven Talent Mapping Assessment) backend system, including End-to-End (E2E) tests and Load testing capabilities.

## 📋 Overview

This testing suite provides:
- **End-to-End Tests**: Complete workflow testing across all services
- **Load Tests**: Performance and scalability testing
- **Comprehensive Reporting**: HTML, JSON, and CSV reports
- **Rate Limiting Validation**: Ensures API rate limits work correctly
- **Health Check Monitoring**: Validates all service health endpoints

## 🏗️ Test Coverage

### E2E Tests
- ✅ Authentication & User Management
- ✅ Admin Management
- ✅ School Management
- ✅ Assessment Flow (Submit → Status → Results)
- ✅ Archive Service (Results, Jobs, Statistics)
- ✅ Health & Monitoring
- ✅ Error Handling
- ✅ Rate Limiting

### Load Tests
- ⚡ Authentication Load Testing
- ⚡ Assessment Submission Load Testing
- ⚡ Archive Service Load Testing
- ⚡ Rate Limiting Validation
- ⚡ Concurrent User Simulation
- ⚡ Performance Metrics Collection

## 🚀 Quick Start

### Prerequisites
1. Ensure all ATMA backend services are running:
   - API Gateway (port 3000)
   - Auth Service (port 3001)
   - Archive Service (port 3002)
   - Assessment Service (port 3003)

2. Install dependencies:
```bash
cd testing
npm install
```

### Running Tests

#### Run All Tests (E2E + Load)
```bash
node test-runner.js
```

#### Run Only E2E Tests
```bash
node test-runner.js --no-load
```

#### Run Only Load Tests
```bash
node test-runner.js --no-e2e
```

#### Run Tests Without Reports
```bash
node test-runner.js --no-report
```

#### Custom Output Directory
```bash
node test-runner.js --output ./custom-results
```

### Individual Test Files

#### E2E Tests Only
```bash
node e2e-tests.js
```

#### Load Tests Only
```bash
node load-tests.js
```

## 📊 Test Reports

The testing suite generates comprehensive reports in multiple formats:

### HTML Report
- Visual dashboard with metrics and charts
- Test status overview
- Detailed performance metrics
- Error analysis

### JSON Report
- Machine-readable format
- Complete test data
- Suitable for CI/CD integration

### CSV Report
- Load test metrics in spreadsheet format
- Performance data analysis
- Easy import into analytics tools

## ⚙️ Configuration

### Environment Configuration
Edit `test-config.js` to modify:
- Base URLs for different environments
- Test user credentials
- Load test parameters
- Performance thresholds

### Load Test Scenarios
Available scenarios in `test-config.js`:
- **Light**: 10 users, 5 requests each
- **Medium**: 50 users, 10 requests each
- **Heavy**: 100 users, 20 requests each
- **Stress**: 200 users, 50 requests each

## 📈 Performance Metrics

### Response Time Metrics
- Average response time
- Min/Max response times
- P50, P95, P99 percentiles

### Throughput Metrics
- Requests per second
- Success rate percentage
- Error rate analysis

### Rate Limiting Metrics
- Rate limit threshold validation
- Rate limit response verification
- Rate limit recovery testing

## 🔧 Customization

### Adding New E2E Tests
1. Add test methods to `E2ETestSuite` class in `e2e-tests.js`
2. Follow the existing pattern:
```javascript
async testNewFeature() {
    log('🔍 Testing New Feature...');
    
    const response = await api.get('/api/new-endpoint', {
        headers: { Authorization: `Bearer ${testState.userToken}` }
    });
    
    assert(response.status === 200, 'New feature test failed');
    log('✓ New feature test successful');
}
```

### Adding New Load Tests
1. Add test scenarios to `LoadTestScenarios` class in `load-tests.js`
2. Follow the existing pattern:
```javascript
async runNewLoadTest() {
    console.log('🚀 Running New Load Test...');
    
    this.metrics.reset();
    this.metrics.startTime = Date.now();
    
    // Your load test logic here
    
    this.metrics.endTime = Date.now();
    return this.metrics.getStats();
}
```

## 🐛 Troubleshooting

### Common Issues

#### Services Not Running
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```
**Solution**: Ensure all backend services are running before starting tests.

#### Authentication Failures
```
Assertion failed: User login failed
```
**Solution**: Check if the admin user exists and has correct credentials in the database.

#### Rate Limiting Issues
```
Rate limiting test failed
```
**Solution**: Wait for rate limit windows to reset or adjust test parameters.

### Debug Mode
Add debug logging by setting environment variable:
```bash
DEBUG=true node test-runner.js
```

## 📋 Test Checklist

Before running tests, ensure:
- [ ] All backend services are running
- [ ] Database is accessible and populated with required data
- [ ] Admin user exists with correct credentials
- [ ] Network connectivity to all services
- [ ] Sufficient system resources for load testing

## 🔒 Security Considerations

- Test users are automatically generated with unique identifiers
- No production data is used in tests
- Test data is isolated and can be safely deleted
- Rate limiting tests respect system limits

## 📞 Support

For issues with the testing suite:
1. Check the troubleshooting section above
2. Review test logs and error reports
3. Verify backend service health endpoints
4. Check network connectivity and firewall settings

## 📝 Test Data

### Test Users
- E2E tests create temporary users with format: `e2e-test-{timestamp}@example.com`
- Load tests create users with format: `loadtest-{id}@example.com`
- All test users use password: `testpass123` or `loadtest123`

### Test Schools
- Created with name: `Test School E2E` or `Test School {id}`
- Located in Jakarta, DKI Jakarta
- Automatically cleaned up after tests (if cleanup is implemented)

### Test Assessments
- Use predefined assessment data from `test-config.js`
- Include personality, skills, and cognitive test components
- Generate realistic but fake assessment results

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: ATMA Backend Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd testing
          npm install
      - name: Start backend services
        run: |
          # Start your backend services here
          docker-compose up -d
      - name: Run tests
        run: |
          cd testing
          node test-runner.js
      - name: Upload test reports
        uses: actions/upload-artifact@v2
        with:
          name: test-reports
          path: testing/test-results/
```

## 📊 Metrics Dashboard

The HTML report includes:
- Test execution timeline
- Performance trends
- Error distribution
- Success rate visualization
- Response time histograms

---

**Last Updated**: 2024-01-21  
**Version**: 1.0.0  
**Compatibility**: ATMA Backend API v1.0.0
