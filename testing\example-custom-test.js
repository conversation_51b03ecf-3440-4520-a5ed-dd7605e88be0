/**
 * Example Custom Test
 * 
 * This file demonstrates how to create custom tests using the ATMA testing framework.
 * You can use this as a template for creating your own specific test scenarios.
 */

const { api, log, assert, CONFIG } = require('./e2e-tests');
const { LoadTestUtils, PerformanceMetrics } = require('./load-tests');
const testConfig = require('./test-config');

class CustomTestExample {
    constructor() {
        this.userToken = null;
        this.testData = {};
    }

    async runCustomTests() {
        console.log('🎯 Running Custom Test Examples...\n');
        
        try {
            await this.setupTestUser();
            await this.testCustomWorkflow();
            await this.testSpecificScenario();
            await this.testErrorScenarios();
            await this.cleanup();
            
            console.log('\n✅ All custom tests passed!');
        } catch (error) {
            console.error('\n❌ Custom tests failed:', error.message);
            throw error;
        }
    }

    async setupTestUser() {
        log('🔧 Setting up test user...');
        
        const testUser = testConfig.generators.randomUser('custom');
        
        // Register user
        const registerResponse = await api.post('/api/auth/register', testUser);
        assert(registerResponse.status === 201, 'User registration failed');
        
        // Login user
        const loginResponse = await api.post('/api/auth/login', {
            email: testUser.email,
            password: testUser.password
        });
        
        assert(loginResponse.status === 200, 'User login failed');
        assert(loginResponse.data.data.token, 'No token received');
        
        this.userToken = loginResponse.data.data.token;
        this.testData.user = testUser;
        this.testData.userId = loginResponse.data.data.user.id;
        
        log('✓ Test user setup completed');
    }

    async testCustomWorkflow() {
        log('🔄 Testing custom workflow...');
        
        // Example: Test a specific workflow that combines multiple API calls
        
        // Step 1: Create a school
        const schoolData = testConfig.generators.randomSchool('custom');
        const schoolResponse = await api.post('/api/auth/schools', schoolData, {
            headers: { Authorization: `Bearer ${this.userToken}` }
        });
        
        assert(schoolResponse.status === 201, 'School creation failed');
        this.testData.schoolId = schoolResponse.data.data.id;
        log('✓ School created successfully');
        
        // Step 2: Update user profile with school
        const profileUpdateResponse = await api.put('/api/auth/profile', {
            school_id: this.testData.schoolId,
            full_name: 'Custom Test User'
        }, {
            headers: { Authorization: `Bearer ${this.userToken}` }
        });
        
        assert(profileUpdateResponse.status === 200, 'Profile update failed');
        log('✓ Profile updated with school');
        
        // Step 3: Submit assessment
        const assessmentData = testConfig.generators.randomAssessment();
        const assessmentResponse = await api.post('/api/assessment/submit', assessmentData, {
            headers: {
                Authorization: `Bearer ${this.userToken}`,
                'X-Idempotency-Key': `custom-test-${Date.now()}`
            }
        });
        
        assert(assessmentResponse.status === 200, 'Assessment submission failed');
        this.testData.jobId = assessmentResponse.data.data.jobId;
        log('✓ Assessment submitted successfully');
        
        // Step 4: Check assessment status
        const statusResponse = await api.get(`/api/assessment/status/${this.testData.jobId}`, {
            headers: { Authorization: `Bearer ${this.userToken}` }
        });
        
        assert(statusResponse.status === 200, 'Assessment status check failed');
        log('✓ Assessment status checked');
        
        log('✓ Custom workflow completed successfully');
    }

    async testSpecificScenario() {
        log('🎯 Testing specific scenario...');
        
        // Example: Test edge cases or specific business logic
        
        // Test token balance before and after assessment
        const balanceBefore = await api.get('/api/auth/token-balance', {
            headers: { Authorization: `Bearer ${this.userToken}` }
        });
        
        assert(balanceBefore.status === 200, 'Token balance check failed');
        const initialBalance = balanceBefore.data.data.balance;
        log(`✓ Initial token balance: ${initialBalance}`);
        
        // Submit another assessment
        const assessmentData = testConfig.generators.randomAssessment();
        const assessmentResponse = await api.post('/api/assessment/submit', assessmentData, {
            headers: {
                Authorization: `Bearer ${this.userToken}`,
                'X-Idempotency-Key': `scenario-test-${Date.now()}`
            }
        });
        
        if (assessmentResponse.status === 200) {
            // Check token balance after assessment
            const balanceAfter = await api.get('/api/auth/token-balance', {
                headers: { Authorization: `Bearer ${this.userToken}` }
            });
            
            assert(balanceAfter.status === 200, 'Token balance check after assessment failed');
            const finalBalance = balanceAfter.data.data.balance;
            
            // Verify token was deducted
            assert(finalBalance < initialBalance, 'Token was not deducted after assessment');
            log(`✓ Token balance after assessment: ${finalBalance} (deducted: ${initialBalance - finalBalance})`);
        }
        
        log('✓ Specific scenario test completed');
    }

    async testErrorScenarios() {
        log('❌ Testing error scenarios...');
        
        // Test invalid assessment submission
        const invalidAssessment = {
            assessmentName: '', // Invalid empty name
            personalityTest: null // Invalid null data
        };
        
        const invalidResponse = await api.post('/api/assessment/submit', invalidAssessment, {
            headers: {
                Authorization: `Bearer ${this.userToken}`,
                'X-Idempotency-Key': `error-test-${Date.now()}`
            }
        });
        
        assert(invalidResponse.status === 400, 'Invalid assessment should return 400');
        log('✓ Invalid assessment properly rejected');
        
        // Test unauthorized access
        const unauthorizedResponse = await api.get('/api/auth/profile');
        assert(unauthorizedResponse.status === 401, 'Unauthorized access should return 401');
        log('✓ Unauthorized access properly blocked');
        
        // Test non-existent resource
        const notFoundResponse = await api.get('/api/archive/results/non-existent-id', {
            headers: { Authorization: `Bearer ${this.userToken}` }
        });
        assert(notFoundResponse.status === 404, 'Non-existent resource should return 404');
        log('✓ Non-existent resource properly handled');
        
        log('✓ Error scenarios test completed');
    }

    async cleanup() {
        log('🧹 Cleaning up test data...');
        
        // Note: In a real implementation, you might want to clean up test data
        // For this example, we'll just log the cleanup
        
        log(`✓ Test user: ${this.testData.user?.email}`);
        log(`✓ Test school ID: ${this.testData.schoolId}`);
        log(`✓ Test job ID: ${this.testData.jobId}`);
        
        // In production, you might delete test data here:
        // await this.deleteTestUser();
        // await this.deleteTestSchool();
        
        log('✓ Cleanup completed (test data logged for manual cleanup if needed)');
    }
}

// Custom Load Test Example
class CustomLoadTestExample {
    constructor() {
        this.metrics = new PerformanceMetrics();
    }

    async runCustomLoadTest() {
        console.log('⚡ Running Custom Load Test Example...\n');
        
        this.metrics.reset();
        this.metrics.startTime = Date.now();
        
        try {
            // Example: Test a specific endpoint under load
            await this.testHealthEndpointLoad();
            await this.testAuthenticationLoad();
            
            this.metrics.endTime = Date.now();
            const stats = this.metrics.getStats();
            
            this.printLoadTestResults(stats);
            return stats;
            
        } catch (error) {
            console.error('Custom load test failed:', error.message);
            throw error;
        }
    }

    async testHealthEndpointLoad() {
        log('🔍 Testing health endpoint under load...');
        
        const promises = [];
        const requestCount = 100;
        
        for (let i = 0; i < requestCount; i++) {
            promises.push(this.makeHealthRequest(i));
        }
        
        await Promise.all(promises);
        log(`✓ Completed ${requestCount} health endpoint requests`);
    }

    async makeHealthRequest(index) {
        const result = await LoadTestUtils.makeRequest('GET', `${CONFIG.BASE_URL}/health`);
        
        this.metrics.recordRequest(
            '/health',
            'GET',
            result.responseTime,
            result.status,
            result.success
        );
        
        if (!result.success) {
            this.metrics.recordError('/health', 'GET', new Error(`Request ${index} failed: ${result.status}`));
        }
    }

    async testAuthenticationLoad() {
        log('🔐 Testing authentication under load...');
        
        const promises = [];
        const userCount = 20;
        
        for (let i = 0; i < userCount; i++) {
            promises.push(this.simulateUserAuth(i));
        }
        
        await Promise.all(promises);
        log(`✓ Completed authentication test for ${userCount} users`);
    }

    async simulateUserAuth(userId) {
        try {
            const user = testConfig.generators.randomUser(userId);
            
            // Register
            const registerResult = await LoadTestUtils.makeRequest(
                'POST',
                `${CONFIG.BASE_URL}/api/auth/register`,
                user
            );
            
            this.metrics.recordRequest(
                '/api/auth/register',
                'POST',
                registerResult.responseTime,
                registerResult.status,
                registerResult.success
            );
            
            if (registerResult.success) {
                // Login
                const loginResult = await LoadTestUtils.makeRequest(
                    'POST',
                    `${CONFIG.BASE_URL}/api/auth/login`,
                    user
                );
                
                this.metrics.recordRequest(
                    '/api/auth/login',
                    'POST',
                    loginResult.responseTime,
                    loginResult.status,
                    loginResult.success
                );
            }
        } catch (error) {
            this.metrics.recordError('auth-flow', 'FLOW', error);
        }
    }

    printLoadTestResults(stats) {
        console.log('\n📊 Custom Load Test Results:');
        console.log(`   Total Requests: ${stats.totalRequests}`);
        console.log(`   Success Rate: ${stats.successRate.toFixed(2)}%`);
        console.log(`   Avg Response Time: ${stats.avgResponseTime}ms`);
        console.log(`   Requests/Second: ${stats.requestsPerSecond}`);
        console.log(`   P95 Response Time: ${stats.p95ResponseTime}ms`);
        
        if (stats.errors.length > 0) {
            console.log(`   Errors: ${stats.errors.length}`);
        }
    }
}

// Main execution function
async function runCustomTests() {
    console.log('🚀 Starting Custom Test Examples\n');
    
    try {
        // Run custom E2E tests
        const e2eTests = new CustomTestExample();
        await e2eTests.runCustomTests();
        
        // Run custom load tests
        const loadTests = new CustomLoadTestExample();
        await loadTests.runCustomLoadTest();
        
        console.log('\n🎉 All custom tests completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Custom tests failed:', error.message);
        process.exit(1);
    }
}

// Export classes for use in other files
module.exports = {
    CustomTestExample,
    CustomLoadTestExample
};

// Run tests if this file is executed directly
if (require.main === module) {
    runCustomTests();
}
