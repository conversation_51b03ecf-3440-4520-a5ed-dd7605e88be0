/**
 * ATMA Backend Load Testing Suite
 * 
 * This file contains comprehensive load tests for the ATMA API Gateway
 * to test performance, scalability, and rate limiting behavior.
 * 
 * Test Scenarios:
 * - Authentication load testing
 * - Assessment submission load testing
 * - Archive service load testing
 * - Rate limiting validation
 * - Concurrent user simulation
 * - Stress testing
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const LOAD_CONFIG = {
    BASE_URL: 'http://localhost:3000',
    TIMEOUT: 30000,
    
    // Load test parameters
    CONCURRENT_USERS: 50,
    REQUESTS_PER_USER: 10,
    RAMP_UP_TIME: 5000, // 5 seconds
    TEST_DURATION: 60000, // 60 seconds
    
    // Rate limiting thresholds (from API documentation)
    RATE_LIMITS: {
        GENERAL: { requests: 5000, window: 15 * 60 * 1000 }, // 15 minutes
        AUTH: { requests: 100, window: 15 * 60 * 1000 },
        ADMIN: { requests: 50, window: 15 * 60 * 1000 },
        ASSESSMENT: { requests: 100, window: 15 * 60 * 1000 },
        ARCHIVE: { requests: 5000, window: 15 * 60 * 1000 }
    },
    
    // Test user template
    TEST_USER_TEMPLATE: {
        email: 'loadtest-{id}@example.com',
        password: 'loadtest123'
    }
};

// Performance metrics
class PerformanceMetrics {
    constructor() {
        this.reset();
    }

    reset() {
        this.requests = [];
        this.errors = [];
        this.startTime = null;
        this.endTime = null;
    }

    recordRequest(endpoint, method, responseTime, statusCode, success) {
        this.requests.push({
            endpoint,
            method,
            responseTime,
            statusCode,
            success,
            timestamp: Date.now()
        });
    }

    recordError(endpoint, method, error) {
        this.errors.push({
            endpoint,
            method,
            error: error.message,
            timestamp: Date.now()
        });
    }

    getStats() {
        const totalRequests = this.requests.length;
        const successfulRequests = this.requests.filter(r => r.success).length;
        const failedRequests = totalRequests - successfulRequests;
        
        const responseTimes = this.requests.map(r => r.responseTime);
        const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0;
        const minResponseTime = Math.min(...responseTimes) || 0;
        const maxResponseTime = Math.max(...responseTimes) || 0;
        
        // Calculate percentiles
        const sortedTimes = responseTimes.sort((a, b) => a - b);
        const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)] || 0;
        const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0;
        const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;
        
        const duration = this.endTime - this.startTime;
        const requestsPerSecond = totalRequests / (duration / 1000);
        
        return {
            totalRequests,
            successfulRequests,
            failedRequests,
            successRate: (successfulRequests / totalRequests) * 100,
            avgResponseTime: Math.round(avgResponseTime),
            minResponseTime,
            maxResponseTime,
            p50ResponseTime: p50,
            p95ResponseTime: p95,
            p99ResponseTime: p99,
            requestsPerSecond: Math.round(requestsPerSecond * 100) / 100,
            duration,
            errors: this.errors
        };
    }
}

// Load testing utilities
class LoadTestUtils {
    static async makeRequest(method, url, data = null, headers = {}) {
        const startTime = Date.now();
        
        try {
            const config = {
                method,
                url,
                timeout: LOAD_CONFIG.TIMEOUT,
                validateStatus: () => true,
                headers
            };
            
            if (data) {
                config.data = data;
            }
            
            const response = await axios(config);
            const responseTime = Date.now() - startTime;
            const success = response.status >= 200 && response.status < 400;
            
            return {
                success,
                status: response.status,
                responseTime,
                data: response.data
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                success: false,
                status: 0,
                responseTime,
                error: error.message
            };
        }
    }

    static async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    static generateTestUser(id) {
        return {
            email: LOAD_CONFIG.TEST_USER_TEMPLATE.email.replace('{id}', id),
            password: LOAD_CONFIG.TEST_USER_TEMPLATE.password
        };
    }

    static async registerAndLoginUser(userId) {
        const user = this.generateTestUser(userId);
        
        // Register
        const registerResult = await this.makeRequest(
            'POST',
            `${LOAD_CONFIG.BASE_URL}/api/auth/register`,
            user
        );
        
        if (!registerResult.success) {
            throw new Error(`Registration failed for user ${userId}: ${registerResult.status}`);
        }
        
        // Login
        const loginResult = await this.makeRequest(
            'POST',
            `${LOAD_CONFIG.BASE_URL}/api/auth/login`,
            user
        );
        
        if (!loginResult.success || !loginResult.data.data?.token) {
            throw new Error(`Login failed for user ${userId}: ${loginResult.status}`);
        }
        
        return {
            user,
            token: loginResult.data.data.token,
            userId: loginResult.data.data.user.id
        };
    }
}

// Load test scenarios
class LoadTestScenarios {
    constructor() {
        this.metrics = new PerformanceMetrics();
    }

    async runAuthenticationLoadTest() {
        console.log('🔐 Running Authentication Load Test...');
        
        this.metrics.reset();
        this.metrics.startTime = Date.now();
        
        const promises = [];
        
        for (let i = 0; i < LOAD_CONFIG.CONCURRENT_USERS; i++) {
            promises.push(this.simulateUserAuthFlow(i));
            
            // Ramp up gradually
            if (i < LOAD_CONFIG.CONCURRENT_USERS - 1) {
                await LoadTestUtils.sleep(LOAD_CONFIG.RAMP_UP_TIME / LOAD_CONFIG.CONCURRENT_USERS);
            }
        }
        
        await Promise.all(promises);
        this.metrics.endTime = Date.now();
        
        return this.metrics.getStats();
    }

    async simulateUserAuthFlow(userId) {
        try {
            const user = LoadTestUtils.generateTestUser(userId);
            
            // Register
            const registerResult = await LoadTestUtils.makeRequest(
                'POST',
                `${LOAD_CONFIG.BASE_URL}/api/auth/register`,
                user
            );
            
            this.metrics.recordRequest(
                '/api/auth/register',
                'POST',
                registerResult.responseTime,
                registerResult.status,
                registerResult.success
            );
            
            if (!registerResult.success) {
                this.metrics.recordError('/api/auth/register', 'POST', new Error(`Status: ${registerResult.status}`));
                return;
            }
            
            // Login multiple times
            for (let i = 0; i < LOAD_CONFIG.REQUESTS_PER_USER; i++) {
                const loginResult = await LoadTestUtils.makeRequest(
                    'POST',
                    `${LOAD_CONFIG.BASE_URL}/api/auth/login`,
                    user
                );
                
                this.metrics.recordRequest(
                    '/api/auth/login',
                    'POST',
                    loginResult.responseTime,
                    loginResult.status,
                    loginResult.success
                );
                
                if (!loginResult.success) {
                    this.metrics.recordError('/api/auth/login', 'POST', new Error(`Status: ${loginResult.status}`));
                    continue;
                }
                
                const token = loginResult.data.data?.token;
                if (token) {
                    // Get profile
                    const profileResult = await LoadTestUtils.makeRequest(
                        'GET',
                        `${LOAD_CONFIG.BASE_URL}/api/auth/profile`,
                        null,
                        { Authorization: `Bearer ${token}` }
                    );
                    
                    this.metrics.recordRequest(
                        '/api/auth/profile',
                        'GET',
                        profileResult.responseTime,
                        profileResult.status,
                        profileResult.success
                    );
                }
                
                // Small delay between requests
                await LoadTestUtils.sleep(100);
            }
        } catch (error) {
            this.metrics.recordError('auth-flow', 'FLOW', error);
        }
    }

    async runAssessmentLoadTest() {
        console.log('🎯 Running Assessment Load Test...');
        
        this.metrics.reset();
        this.metrics.startTime = Date.now();
        
        // First, create authenticated users
        const users = [];
        for (let i = 0; i < Math.min(LOAD_CONFIG.CONCURRENT_USERS, 20); i++) {
            try {
                const userAuth = await LoadTestUtils.registerAndLoginUser(i);
                users.push(userAuth);
            } catch (error) {
                console.warn(`Failed to create user ${i}:`, error.message);
            }
        }
        
        console.log(`Created ${users.length} authenticated users`);
        
        const promises = users.map((userAuth, index) => 
            this.simulateAssessmentFlow(userAuth, index)
        );
        
        await Promise.all(promises);
        this.metrics.endTime = Date.now();
        
        return this.metrics.getStats();
    }

    async simulateAssessmentFlow(userAuth, userIndex) {
        try {
            for (let i = 0; i < 3; i++) { // 3 assessments per user
                const assessmentData = {
                    assessmentName: `Load Test Assessment ${userIndex}-${i}`,
                    personalityTest: {
                        responses: Array.from({length: 10}, () => Math.floor(Math.random() * 5) + 1),
                        completedAt: new Date().toISOString()
                    },
                    skillsAssessment: {
                        technicalSkills: { programming: Math.floor(Math.random() * 10) + 1 },
                        softSkills: { communication: Math.floor(Math.random() * 10) + 1 }
                    },
                    cognitiveTest: {
                        logicalReasoning: { score: Math.floor(Math.random() * 100) + 1 },
                        problemSolving: { score: Math.floor(Math.random() * 100) + 1 }
                    }
                };
                
                const submitResult = await LoadTestUtils.makeRequest(
                    'POST',
                    `${LOAD_CONFIG.BASE_URL}/api/assessment/submit`,
                    assessmentData,
                    {
                        Authorization: `Bearer ${userAuth.token}`,
                        'X-Idempotency-Key': uuidv4()
                    }
                );
                
                this.metrics.recordRequest(
                    '/api/assessment/submit',
                    'POST',
                    submitResult.responseTime,
                    submitResult.status,
                    submitResult.success
                );
                
                if (submitResult.success && submitResult.data.data?.jobId) {
                    // Check status
                    const statusResult = await LoadTestUtils.makeRequest(
                        'GET',
                        `${LOAD_CONFIG.BASE_URL}/api/assessment/status/${submitResult.data.data.jobId}`,
                        null,
                        { Authorization: `Bearer ${userAuth.token}` }
                    );
                    
                    this.metrics.recordRequest(
                        '/api/assessment/status',
                        'GET',
                        statusResult.responseTime,
                        statusResult.status,
                        statusResult.success
                    );
                }
                
                await LoadTestUtils.sleep(1000); // 1 second between assessments
            }
        } catch (error) {
            this.metrics.recordError('assessment-flow', 'FLOW', error);
        }
    }

    async runArchiveLoadTest() {
        console.log('📊 Running Archive Load Test...');
        
        this.metrics.reset();
        this.metrics.startTime = Date.now();
        
        // Create a few authenticated users
        const users = [];
        for (let i = 0; i < Math.min(LOAD_CONFIG.CONCURRENT_USERS, 10); i++) {
            try {
                const userAuth = await LoadTestUtils.registerAndLoginUser(i);
                users.push(userAuth);
            } catch (error) {
                console.warn(`Failed to create user ${i}:`, error.message);
            }
        }
        
        const promises = users.map((userAuth, index) => 
            this.simulateArchiveFlow(userAuth, index)
        );
        
        await Promise.all(promises);
        this.metrics.endTime = Date.now();
        
        return this.metrics.getStats();
    }

    async simulateArchiveFlow(userAuth, userIndex) {
        try {
            const endpoints = [
                '/api/archive/results?page=1&limit=10',
                '/api/archive/jobs?page=1&limit=10',
                '/api/archive/jobs/stats',
                '/api/archive/stats',
                '/api/archive/stats/overview',
                '/api/archive/api/v1/stats?type=user&scope=overview&timeRange=30%20days'
            ];
            
            for (let i = 0; i < LOAD_CONFIG.REQUESTS_PER_USER; i++) {
                for (const endpoint of endpoints) {
                    const result = await LoadTestUtils.makeRequest(
                        'GET',
                        `${LOAD_CONFIG.BASE_URL}${endpoint}`,
                        null,
                        { Authorization: `Bearer ${userAuth.token}` }
                    );
                    
                    this.metrics.recordRequest(
                        endpoint,
                        'GET',
                        result.responseTime,
                        result.status,
                        result.success
                    );
                    
                    await LoadTestUtils.sleep(50); // Small delay between requests
                }
            }
        } catch (error) {
            this.metrics.recordError('archive-flow', 'FLOW', error);
        }
    }

    async runRateLimitTest() {
        console.log('🚦 Running Rate Limit Test...');
        
        this.metrics.reset();
        this.metrics.startTime = Date.now();
        
        // Test auth endpoint rate limiting (100 requests per 15 minutes)
        const promises = [];
        const requestCount = 120; // Exceed the limit
        
        for (let i = 0; i < requestCount; i++) {
            promises.push(this.makeRateLimitRequest(i));
        }
        
        await Promise.all(promises);
        this.metrics.endTime = Date.now();
        
        const stats = this.metrics.getStats();
        
        // Check if rate limiting is working
        const rateLimitedRequests = this.metrics.requests.filter(r => r.statusCode === 429);
        stats.rateLimitedRequests = rateLimitedRequests.length;
        stats.rateLimitingWorking = rateLimitedRequests.length > 0;
        
        return stats;
    }

    async makeRateLimitRequest(index) {
        const result = await LoadTestUtils.makeRequest(
            'POST',
            `${LOAD_CONFIG.BASE_URL}/api/auth/login`,
            {
                email: '<EMAIL>',
                password: 'wrongpassword'
            }
        );
        
        this.metrics.recordRequest(
            '/api/auth/login',
            'POST',
            result.responseTime,
            result.status,
            result.status === 429 || result.status === 401 // Both are acceptable
        );
    }
}

// Main load test runner
class LoadTestRunner {
    async runAllLoadTests() {
        console.log('🚀 Starting ATMA Backend Load Tests\n');
        
        const scenarios = new LoadTestScenarios();
        const results = {};
        
        try {
            // Authentication load test
            console.log('Running authentication load test...');
            results.authentication = await scenarios.runAuthenticationLoadTest();
            this.printResults('Authentication Load Test', results.authentication);
            
            await LoadTestUtils.sleep(2000); // Cool down
            
            // Assessment load test
            console.log('\nRunning assessment load test...');
            results.assessment = await scenarios.runAssessmentLoadTest();
            this.printResults('Assessment Load Test', results.assessment);
            
            await LoadTestUtils.sleep(2000); // Cool down
            
            // Archive load test
            console.log('\nRunning archive load test...');
            results.archive = await scenarios.runArchiveLoadTest();
            this.printResults('Archive Load Test', results.archive);
            
            await LoadTestUtils.sleep(2000); // Cool down
            
            // Rate limit test
            console.log('\nRunning rate limit test...');
            results.rateLimit = await scenarios.runRateLimitTest();
            this.printResults('Rate Limit Test', results.rateLimit);
            
            console.log('\n✅ All load tests completed successfully!');
            return results;
            
        } catch (error) {
            console.error('\n❌ Load tests failed:', error.message);
            throw error;
        }
    }

    printResults(testName, stats) {
        console.log(`\n📊 ${testName} Results:`);
        console.log(`   Total Requests: ${stats.totalRequests}`);
        console.log(`   Successful: ${stats.successfulRequests} (${stats.successRate.toFixed(2)}%)`);
        console.log(`   Failed: ${stats.failedRequests}`);
        console.log(`   Avg Response Time: ${stats.avgResponseTime}ms`);
        console.log(`   Min/Max Response Time: ${stats.minResponseTime}ms / ${stats.maxResponseTime}ms`);
        console.log(`   P50/P95/P99: ${stats.p50ResponseTime}ms / ${stats.p95ResponseTime}ms / ${stats.p99ResponseTime}ms`);
        console.log(`   Requests/Second: ${stats.requestsPerSecond}`);
        console.log(`   Duration: ${stats.duration}ms`);
        
        if (stats.rateLimitedRequests !== undefined) {
            console.log(`   Rate Limited Requests: ${stats.rateLimitedRequests}`);
            console.log(`   Rate Limiting Working: ${stats.rateLimitingWorking ? 'Yes' : 'No'}`);
        }
        
        if (stats.errors.length > 0) {
            console.log(`   Errors: ${stats.errors.length}`);
            stats.errors.slice(0, 5).forEach(error => {
                console.log(`     - ${error.endpoint} ${error.method}: ${error.error}`);
            });
        }
    }
}

// Export for use in other files
module.exports = {
    LoadTestRunner,
    LoadTestScenarios,
    LoadTestUtils,
    PerformanceMetrics,
    LOAD_CONFIG
};

// Run tests if this file is executed directly
if (require.main === module) {
    const runner = new LoadTestRunner();
    runner.runAllLoadTests()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
}
