@echo off
REM ATMA Backend Testing Suite Runner for Windows
REM This script provides easy commands to run different types of tests

setlocal enabledelayedexpansion

REM Function to print colored output (basic version for Windows)
set "INFO_PREFIX=[INFO]"
set "SUCCESS_PREFIX=[SUCCESS]"
set "WARNING_PREFIX=[WARNING]"
set "ERROR_PREFIX=[ERROR]"

REM Function to check if Node.js is installed
:check_node
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo %ERROR_PREFIX% Node.js is not installed. Please install Node.js 16 or higher.
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
echo %INFO_PREFIX% Node.js version: %NODE_VERSION%
goto :eof

REM Function to check if dependencies are installed
:check_dependencies
if not exist "node_modules" (
    echo %WARNING_PREFIX% Dependencies not found. Installing...
    call npm install
    if %errorlevel% neq 0 (
        echo %ERROR_PREFIX% Failed to install dependencies
        exit /b 1
    )
)
goto :eof

REM Function to check if backend services are running
:check_services
echo %INFO_PREFIX% Checking backend services...

REM Check API Gateway
curl -s http://localhost:3000/health >nul 2>nul
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% API Gateway is running (port 3000)
) else (
    echo %ERROR_PREFIX% API Gateway is not running on port 3000
    echo %INFO_PREFIX% Please start the API Gateway before running tests
    exit /b 1
)

REM Check individual services
set services=auth:3001 archive:3002 assessment:3003
for %%s in (%services%) do (
    for /f "tokens=1,2 delims=:" %%a in ("%%s") do (
        curl -s http://localhost:%%b/health >nul 2>nul
        if !errorlevel! equ 0 (
            echo %SUCCESS_PREFIX% %%a service is running (port %%b)
        ) else (
            echo %WARNING_PREFIX% %%a service may not be running on port %%b
        )
    )
)
goto :eof

REM Function to show help
:show_help
echo ATMA Backend Testing Suite
echo.
echo Usage: %~nx0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   all                Run all tests (E2E + Load)
echo   e2e                Run only E2E tests
echo   load               Run only load tests
echo   custom             Run custom test examples
echo   health             Check backend services health
echo   install            Install dependencies
echo   help               Show this help message
echo.
echo Options:
echo   --no-report        Skip generating test reports
echo   --output DIR       Specify output directory for reports
echo   --scenario NAME    Load test scenario (light^|medium^|heavy^|stress)
echo.
echo Examples:
echo   %~nx0 all                    # Run all tests
echo   %~nx0 e2e                    # Run only E2E tests
echo   %~nx0 load --scenario light  # Run light load tests
echo   %~nx0 all --no-report        # Run all tests without reports
echo.
goto :eof

REM Function to run all tests
:run_all_tests
echo %INFO_PREFIX% Running all tests (E2E + Load)...
node test-runner.js %*
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% All tests completed!
) else (
    echo %ERROR_PREFIX% Tests failed!
    exit /b 1
)
goto :eof

REM Function to run E2E tests only
:run_e2e_tests
echo %INFO_PREFIX% Running E2E tests only...
node test-runner.js --no-load %*
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% E2E tests completed!
) else (
    echo %ERROR_PREFIX% E2E tests failed!
    exit /b 1
)
goto :eof

REM Function to run load tests only
:run_load_tests
echo %INFO_PREFIX% Running load tests only...
node test-runner.js --no-e2e %*
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% Load tests completed!
) else (
    echo %ERROR_PREFIX% Load tests failed!
    exit /b 1
)
goto :eof

REM Function to run custom tests
:run_custom_tests
echo %INFO_PREFIX% Running custom test examples...
node example-custom-test.js
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% Custom tests completed!
) else (
    echo %ERROR_PREFIX% Custom tests failed!
    exit /b 1
)
goto :eof

REM Function to check health
:check_health
echo %INFO_PREFIX% Checking system health...
call :check_node
call :check_dependencies
call :check_services
echo %SUCCESS_PREFIX% Health check completed!
goto :eof

REM Function to install dependencies
:install_deps
echo %INFO_PREFIX% Installing dependencies...
call npm install
if %errorlevel% equ 0 (
    echo %SUCCESS_PREFIX% Dependencies installed!
) else (
    echo %ERROR_PREFIX% Failed to install dependencies!
    exit /b 1
)
goto :eof

REM Main script logic
:main
REM Change to script directory
cd /d "%~dp0"

REM Parse command
set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="all" (
    call :check_node
    call :check_dependencies
    call :check_services
    shift
    call :run_all_tests %*
) else if "%command%"=="e2e" (
    call :check_node
    call :check_dependencies
    call :check_services
    shift
    call :run_e2e_tests %*
) else if "%command%"=="load" (
    call :check_node
    call :check_dependencies
    call :check_services
    shift
    call :run_load_tests %*
) else if "%command%"=="custom" (
    call :check_node
    call :check_dependencies
    call :check_services
    call :run_custom_tests
) else if "%command%"=="health" (
    call :check_health
) else if "%command%"=="install" (
    call :check_node
    call :install_deps
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else (
    echo %ERROR_PREFIX% Unknown command: %command%
    echo.
    call :show_help
    exit /b 1
)

goto :eof

REM Call main function
call :main %*
