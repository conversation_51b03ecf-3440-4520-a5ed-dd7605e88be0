{"name": "atma-backend-testing", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend Testing Suite", "main": "test-runner.js", "scripts": {"test": "node test-runner.js", "test:e2e": "node e2e-tests.js", "test:load": "node load-tests.js", "test:e2e-only": "node test-runner.js --no-load", "test:load-only": "node test-runner.js --no-e2e", "test:no-report": "node test-runner.js --no-report", "help": "echo 'Available scripts: test, test:e2e, test:load, test:e2e-only, test:load-only, test:no-report'"}, "keywords": ["testing", "e2e", "load-testing", "api-testing", "atma", "backend"], "author": "ATMA Development Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "socket.io-client": "^4.7.5", "uuid": "^11.1.0"}, "engines": {"node": ">=16.0.0"}}