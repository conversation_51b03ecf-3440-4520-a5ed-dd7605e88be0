#!/bin/bash

# ATMA Backend Testing Suite Runner
# This script provides easy commands to run different types of tests

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js version 16 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_info "Node.js version: $(node --version)"
}

# Function to check if dependencies are installed
check_dependencies() {
    if [ ! -d "node_modules" ]; then
        print_warning "Dependencies not found. Installing..."
        npm install
    fi
}

# Function to check if backend services are running
check_services() {
    print_info "Checking backend services..."
    
    # Check API Gateway
    if curl -s http://localhost:3000/health > /dev/null 2>&1; then
        print_success "API Gateway is running (port 3000)"
    else
        print_error "API Gateway is not running on port 3000"
        print_info "Please start the API Gateway before running tests"
        exit 1
    fi
    
    # Check individual services
    services=("auth:3001" "archive:3002" "assessment:3003")
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d':' -f1)
        port=$(echo $service | cut -d':' -f2)
        
        if curl -s http://localhost:$port/health > /dev/null 2>&1; then
            print_success "$name service is running (port $port)"
        else
            print_warning "$name service may not be running on port $port"
        fi
    done
}

# Function to show help
show_help() {
    echo "ATMA Backend Testing Suite"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  all                Run all tests (E2E + Load)"
    echo "  e2e                Run only E2E tests"
    echo "  load               Run only load tests"
    echo "  custom             Run custom test examples"
    echo "  health             Check backend services health"
    echo "  install            Install dependencies"
    echo "  help               Show this help message"
    echo ""
    echo "Options:"
    echo "  --no-report        Skip generating test reports"
    echo "  --output DIR       Specify output directory for reports"
    echo "  --scenario NAME    Load test scenario (light|medium|heavy|stress)"
    echo ""
    echo "Examples:"
    echo "  $0 all                    # Run all tests"
    echo "  $0 e2e                    # Run only E2E tests"
    echo "  $0 load --scenario light  # Run light load tests"
    echo "  $0 all --no-report        # Run all tests without reports"
    echo ""
}

# Function to run all tests
run_all_tests() {
    print_info "Running all tests (E2E + Load)..."
    node test-runner.js "$@"
    print_success "All tests completed!"
}

# Function to run E2E tests only
run_e2e_tests() {
    print_info "Running E2E tests only..."
    node test-runner.js --no-load "$@"
    print_success "E2E tests completed!"
}

# Function to run load tests only
run_load_tests() {
    print_info "Running load tests only..."
    node test-runner.js --no-e2e "$@"
    print_success "Load tests completed!"
}

# Function to run custom tests
run_custom_tests() {
    print_info "Running custom test examples..."
    node example-custom-test.js
    print_success "Custom tests completed!"
}

# Function to check health
check_health() {
    print_info "Checking system health..."
    check_node
    check_dependencies
    check_services
    print_success "Health check completed!"
}

# Function to install dependencies
install_deps() {
    print_info "Installing dependencies..."
    npm install
    print_success "Dependencies installed!"
}

# Main script logic
main() {
    # Change to script directory
    cd "$(dirname "$0")"
    
    # Parse command
    case "${1:-help}" in
        "all")
            check_node
            check_dependencies
            check_services
            shift
            run_all_tests "$@"
            ;;
        "e2e")
            check_node
            check_dependencies
            check_services
            shift
            run_e2e_tests "$@"
            ;;
        "load")
            check_node
            check_dependencies
            check_services
            shift
            run_load_tests "$@"
            ;;
        "custom")
            check_node
            check_dependencies
            check_services
            run_custom_tests
            ;;
        "health")
            check_health
            ;;
        "install")
            check_node
            install_deps
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Trap errors and provide helpful message
trap 'print_error "Test execution failed. Check the output above for details."' ERR

# Run main function with all arguments
main "$@"
