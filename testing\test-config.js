/**
 * ATMA Backend Test Configuration
 * 
 * Centralized configuration for all testing scenarios
 */

module.exports = {
    // Environment settings
    environment: {
        development: {
            baseUrl: 'http://localhost:3000',
            timeout: 30000,
            retries: 3
        },
        staging: {
            baseUrl: 'https://staging-api.atma.com',
            timeout: 45000,
            retries: 5
        },
        production: {
            baseUrl: 'https://api.atma.com',
            timeout: 60000,
            retries: 5
        }
    },

    // E2E Test Configuration
    e2e: {
        // Test user credentials
        testUser: {
            email: `e2e-test-${Date.now()}@example.com`,
            password: 'e2etest123',
            username: 'e2etestuser',
            fullName: 'E2E Test User'
        },

        // Admin credentials
        adminUser: {
            username: 'admin',
            password: 'admin123'
        },

        // Test data
        testSchool: {
            name: 'E2E Test School',
            address: 'Test Address 123',
            city: 'Jakarta',
            province: 'DKI Jakarta'
        },

        // Assessment test data
        assessmentData: {
            assessmentName: 'E2E Test Assessment',
            personalityTest: {
                responses: [1, 2, 3, 4, 5, 1, 2, 3, 4, 5],
                completedAt: () => new Date().toISOString()
            },
            skillsAssessment: {
                technicalSkills: {
                    programming: 8,
                    databases: 7,
                    webDevelopment: 9,
                    dataAnalysis: 6
                },
                softSkills: {
                    communication: 9,
                    leadership: 6,
                    teamwork: 8,
                    problemSolving: 7
                }
            },
            cognitiveTest: {
                logicalReasoning: { score: 85 },
                problemSolving: { score: 78 },
                memoryTest: { score: 82 },
                attentionTest: { score: 90 }
            }
        },

        // Test timeouts
        timeouts: {
            shortWait: 1000,
            mediumWait: 5000,
            longWait: 30000,
            assessmentProcessing: 120000
        }
    },

    // Load Test Configuration
    load: {
        // Test scenarios
        scenarios: {
            light: {
                concurrentUsers: 10,
                requestsPerUser: 5,
                rampUpTime: 2000,
                testDuration: 30000
            },
            medium: {
                concurrentUsers: 50,
                requestsPerUser: 10,
                rampUpTime: 5000,
                testDuration: 60000
            },
            heavy: {
                concurrentUsers: 100,
                requestsPerUser: 20,
                rampUpTime: 10000,
                testDuration: 120000
            },
            stress: {
                concurrentUsers: 200,
                requestsPerUser: 50,
                rampUpTime: 20000,
                testDuration: 300000
            }
        },

        // Rate limiting thresholds (from API documentation)
        rateLimits: {
            general: { requests: 5000, window: 15 * 60 * 1000 }, // 15 minutes
            auth: { requests: 100, window: 15 * 60 * 1000 },
            admin: { requests: 50, window: 15 * 60 * 1000 },
            assessment: { requests: 100, window: 15 * 60 * 1000 },
            archive: { requests: 5000, window: 15 * 60 * 1000 }
        },

        // Performance thresholds
        performanceThresholds: {
            responseTime: {
                acceptable: 1000, // ms
                good: 500,
                excellent: 200
            },
            successRate: {
                minimum: 95, // %
                good: 98,
                excellent: 99.5
            },
            requestsPerSecond: {
                minimum: 10,
                good: 50,
                excellent: 100
            }
        },

        // Test user template
        userTemplate: {
            email: 'loadtest-{id}@example.com',
            password: 'loadtest123',
            username: 'loaduser{id}',
            fullName: 'Load Test User {id}'
        }
    },

    // API Endpoints
    endpoints: {
        // Health checks
        health: {
            gateway: '/',
            gatewayHealth: '/health',
            gatewayDetailed: '/health/detailed',
            gatewayReady: '/health/ready',
            gatewayLive: '/health/live',
            authHealth: '/api/auth/health',
            archiveHealth: '/api/archive/health',
            assessmentHealth: '/api/assessment/health',
            assessmentReady: '/api/assessment/health/ready',
            assessmentLive: '/api/assessment/health/live',
            assessmentQueue: '/api/assessment/health/queue'
        },

        // Authentication
        auth: {
            register: '/api/auth/register',
            batchRegister: '/api/auth/register/batch',
            login: '/api/auth/login',
            profile: '/api/auth/profile',
            updateProfile: '/api/auth/profile',
            changePassword: '/api/auth/change-password',
            logout: '/api/auth/logout',
            tokenBalance: '/api/auth/token-balance'
        },

        // School management
        schools: {
            list: '/api/auth/schools',
            create: '/api/auth/schools',
            byLocation: '/api/auth/schools/by-location',
            users: '/api/auth/schools/{schoolId}/users'
        },

        // Admin
        admin: {
            login: '/api/admin/login',
            profile: '/api/admin/profile',
            register: '/api/admin/register'
        },

        // Assessment
        assessment: {
            submit: '/api/assessment/submit',
            status: '/api/assessment/status/{jobId}',
            queueStatus: '/api/assessment/queue/status'
        },

        // Archive
        archive: {
            results: '/api/archive/results',
            result: '/api/archive/results/{id}',
            updateResult: '/api/archive/results/{id}',
            deleteResult: '/api/archive/results/{id}',
            jobs: '/api/archive/jobs',
            job: '/api/archive/jobs/{jobId}',
            jobStats: '/api/archive/jobs/stats',
            stats: '/api/archive/stats',
            overview: '/api/archive/stats/overview',
            unifiedStats: '/api/archive/api/v1/stats'
        }
    },

    // Test data generators
    generators: {
        // Generate random assessment data
        randomAssessment: () => ({
            assessmentName: `Random Assessment ${Date.now()}`,
            personalityTest: {
                responses: Array.from({length: 10}, () => Math.floor(Math.random() * 5) + 1),
                completedAt: new Date().toISOString()
            },
            skillsAssessment: {
                technicalSkills: {
                    programming: Math.floor(Math.random() * 10) + 1,
                    databases: Math.floor(Math.random() * 10) + 1,
                    webDevelopment: Math.floor(Math.random() * 10) + 1
                },
                softSkills: {
                    communication: Math.floor(Math.random() * 10) + 1,
                    leadership: Math.floor(Math.random() * 10) + 1,
                    teamwork: Math.floor(Math.random() * 10) + 1
                }
            },
            cognitiveTest: {
                logicalReasoning: { score: Math.floor(Math.random() * 100) + 1 },
                problemSolving: { score: Math.floor(Math.random() * 100) + 1 }
            }
        }),

        // Generate random user data
        randomUser: (id) => ({
            email: `testuser-${id}-${Date.now()}@example.com`,
            password: `testpass${id}123`,
            username: `testuser${id}`,
            fullName: `Test User ${id}`
        }),

        // Generate random school data
        randomSchool: (id) => ({
            name: `Test School ${id}`,
            address: `Test Address ${id}`,
            city: ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang'][Math.floor(Math.random() * 5)],
            province: 'Test Province'
        })
    },

    // Validation rules
    validation: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        password: {
            minLength: 6,
            maxLength: 50
        },
        username: {
            minLength: 3,
            maxLength: 30,
            pattern: /^[a-zA-Z0-9_]+$/
        }
    },

    // Error codes and messages
    errors: {
        validation: 'VALIDATION_ERROR',
        unauthorized: 'UNAUTHORIZED',
        forbidden: 'FORBIDDEN',
        rateLimitExceeded: 'RATE_LIMIT_EXCEEDED',
        serviceUnavailable: 'SERVICE_UNAVAILABLE',
        gatewayTimeout: 'GATEWAY_TIMEOUT',
        insufficientTokens: 'INSUFFICIENT_TOKENS'
    },

    // HTTP status codes
    statusCodes: {
        ok: 200,
        created: 201,
        badRequest: 400,
        unauthorized: 401,
        forbidden: 403,
        notFound: 404,
        conflict: 409,
        rateLimitExceeded: 429,
        internalServerError: 500,
        serviceUnavailable: 503,
        gatewayTimeout: 504
    },

    // CORS configuration
    cors: {
        allowedOrigins: [
            'http://localhost:3000',
            'http://localhost:8080',
            'http://localhost:5173'
        ],
        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Service-Key', 'X-Internal-Service']
    },

    // Report configuration
    reporting: {
        outputFormats: ['json', 'html', 'csv'],
        includeRawData: true,
        includeErrorDetails: true,
        generateCharts: false, // Would require additional dependencies
        timestampFormat: 'YYYY-MM-DD_HH-mm-ss'
    }
};
